*** Settings ***
Documentation    Test case to parse API response JSON and write ghtk_addresses data to Excel based on type field
Library          Collections
Library          OperatingSystem
Library          String

*** Variables ***
# Excel column mapping based on address type
${TYPE_10_COLUMN}    A    # Type 10 addresses go to column A
${TYPE_1_COLUMN}     B    # Type 1 addresses go to column B
${TYPE_3_COLUMN}     C    # Type 3 addresses go to column C
${EXCEL_FILE_PATH}   output_addresses.xlsx

# Sample API response JSON data (as a single line string)
${API_RESPONSE}    {"success": true, "status": "OK", "data": [{"index": 0, "specific_address": "ngo 268 75 pho doi can", "ghtk_addresses": [{"name": "Lẻ 1-135 Đội Cấn", "type": 29, "address_id": 86996}, {"name": "Quận Ba Đình", "type": 3, "address_id": 2}, {"name": "<PERSON>à Nội", "type": 0, "address_id": 1}, {"name": "<PERSON><PERSON><PERSON>", "type": 2, "address_id": 151}, {"name": "<PERSON>ường Độ<PERSON>", "type": 1, "address_id": 33}]}]}

*** Test Cases ***
Parse API Response And Write To Excel
    [Documentation]    Main test case to parse API response and write ghtk_addresses to Excel
    [Tags]    api    excel    json

    # Step 1: Create new Excel workbook using Python
    Log    Creating new Excel file: ${EXCEL_FILE_PATH}
    ${workbook}=    Create Excel Workbook

    # Step 2: Parse JSON API response using Python json module
    Log    Parsing API response JSON data
    ${parsed_json}=    Parse JSON String    ${API_RESPONSE}

    # Step 3: Validate response structure
    Validate API Response Structure    ${parsed_json}

    # Step 4: Extract and process data items
    ${data_items}=    Get From Dictionary    ${parsed_json}    data
    ${data_count}=    Get Length    ${data_items}
    Log    Found ${data_count} data items in API response

    # Step 5: Process each data item
    FOR    ${index}    ${data_item}    IN ENUMERATE    @{data_items}
        Log    Processing data item ${index + 1} of ${data_count}
        Process Data Item    ${data_item}    ${index}    ${workbook}
    END

    # Step 6: Save and close Excel file
    Save Excel Workbook    ${workbook}

    Log    Successfully processed API response and saved to ${EXCEL_FILE_PATH}

Parse API Response From JSON File And Write To Excel
    [Documentation]    Test case to read API response from JSON file and write to Excel
    [Tags]    api    excel    json    file

    # Step 1: Create new Excel workbook
    Log    Creating new Excel file: ${EXCEL_FILE_PATH}
    ${workbook}=    Create Excel Workbook

    # Step 2: Read and parse JSON from file
    Log    Reading API response from sample_api_response.json
    ${json_content}=    Get File    sample_api_response.json
    ${parsed_json}=    Parse JSON String    ${json_content}

    # Step 3: Validate response structure
    Validate API Response Structure    ${parsed_json}

    # Step 4: Extract and process data items
    ${data_items}=    Get From Dictionary    ${parsed_json}    data
    ${data_count}=    Get Length    ${data_items}
    Log    Found ${data_count} data items in API response

    # Step 5: Process each data item
    FOR    ${index}    ${data_item}    IN ENUMERATE    @{data_items}
        Log    Processing data item ${index + 1} of ${data_count}
        Process Data Item    ${data_item}    ${index}    ${workbook}
    END

    # Step 6: Save and close Excel file
    Save Excel Workbook    ${workbook}

    Log    Successfully processed API response from file and saved to ${EXCEL_FILE_PATH}

*** Keywords ***
Create Excel Workbook
    [Documentation]    Creates a new Excel workbook using openpyxl
    [Return]    ${workbook}

    ${workbook}=    Evaluate    __import__('openpyxl').Workbook()
    RETURN    ${workbook}

Parse JSON String
    [Documentation]    Parses a JSON string using Python's json module
    [Arguments]    ${json_string}
    [Return]    ${parsed_json}

    ${parsed_json}=    Evaluate    __import__('json').loads('''${json_string}''')
    RETURN    ${parsed_json}

Save Excel Workbook
    [Documentation]    Saves the Excel workbook to a file
    [Arguments]    ${workbook}

    Evaluate    $workbook.save('${EXCEL_FILE_PATH}')
    Log    Excel file saved to: ${EXCEL_FILE_PATH}

Validate API Response Structure
    [Documentation]    Validates that the API response has the expected structure
    [Arguments]    ${json_data}

    # Check if response is successful
    ${success}=    Get From Dictionary    ${json_data}    success    default=${False}
    Should Be True    ${success}    API response indicates failure

    # Check if data field exists and is a list
    Dictionary Should Contain Key    ${json_data}    data    API response missing 'data' field
    ${data}=    Get From Dictionary    ${json_data}    data    default=${None}
    ${is_list}=    Evaluate    isinstance($data, list)
    Should Be True    ${is_list}    'data' field should be a list

Process Data Item
    [Documentation]    Processes a single data item and extracts ghtk_addresses
    [Arguments]    ${data_item}    ${item_index}    ${workbook}

    # Validate data item structure
    Dictionary Should Contain Key    ${data_item}    ghtk_addresses
    ...    Data item ${item_index} missing 'ghtk_addresses' field

    # Get ghtk_addresses array
    ${ghtk_addresses}=    Get From Dictionary    ${data_item}    ghtk_addresses    default=${None}
    ${address_count}=    Get Length    ${ghtk_addresses}
    Log    Found ${address_count} addresses in data item ${item_index}

    # Get the active worksheet
    ${worksheet}=    Evaluate    $workbook.active

    # Initialize row counters for each type (to handle multiple addresses of same type)
    ${type_10_row}=    Set Variable    ${1}
    ${type_1_row}=     Set Variable    ${1}
    ${type_3_row}=     Set Variable    ${1}

    # Process each address in the ghtk_addresses array
    FOR    ${address}    IN    @{ghtk_addresses}
        ${type_10_row}    ${type_1_row}    ${type_3_row}=    Process Address
        ...    ${address}    ${type_10_row}    ${type_1_row}    ${type_3_row}    ${worksheet}
    END

Process Address
    [Documentation]    Processes a single address and writes to appropriate Excel cell based on type
    [Arguments]    ${address}    ${type_10_row}    ${type_1_row}    ${type_3_row}    ${worksheet}

    # Validate address structure
    Dictionary Should Contain Key    ${address}    name    Address missing 'name' field
    Dictionary Should Contain Key    ${address}    type    Address missing 'type' field

    # Extract name and type
    ${name}=    Get From Dictionary    ${address}    name    default=${None}
    ${type}=    Get From Dictionary    ${address}    type    default=${None}

    Log    Processing address: ${name} (type: ${type})

    # Write to Excel based on type and update row counter
    IF    ${type} == 10
        ${cell}=    Set Variable    ${TYPE_10_COLUMN}${type_10_row}
        Write Excel Cell    ${worksheet}    ${cell}    ${name}
        Log    Written '${name}' to cell ${cell} (type 10)
        ${type_10_row}=    Evaluate    ${type_10_row} + 1

    ELSE IF    ${type} == 1
        ${cell}=    Set Variable    ${TYPE_1_COLUMN}${type_1_row}
        Write Excel Cell    ${worksheet}    ${cell}    ${name}
        Log    Written '${name}' to cell ${cell} (type 1)
        ${type_1_row}=    Evaluate    ${type_1_row} + 1

    ELSE IF    ${type} == 3
        ${cell}=    Set Variable    ${TYPE_3_COLUMN}${type_3_row}
        Write Excel Cell    ${worksheet}    ${cell}    ${name}
        Log    Written '${name}' to cell ${cell} (type 3)
        ${type_3_row}=    Evaluate    ${type_3_row} + 1

    ELSE
        Log    Skipping address '${name}' with unsupported type: ${type}    WARN
    END

    # Return updated row counters
    RETURN    ${type_10_row}    ${type_1_row}    ${type_3_row}

Write Excel Cell
    [Documentation]    Writes a value to a specific Excel cell using openpyxl
    [Arguments]    ${worksheet}    ${cell}    ${value}

    TRY
        Evaluate    setattr($worksheet['${cell}'], 'value', '''${value}''')
        Log    Successfully wrote '${value}' to cell ${cell}
    EXCEPT    AS    ${error}
        Log    Failed to write to cell ${cell}: ${error}    ERROR
        Fail    Excel write operation failed: ${error}
    END
