<?xml version="1.0" encoding="UTF-8"?>
<robot generator="Robot 7.0 (Python 3.12.2 on darwin)" generated="2025-05-29T21:08:02.314382" rpa="false" schemaversion="5">
<suite id="s1" name="Test Api Response To Excel" source="/Users/<USER>/Documents/robot_api_extractdata/test_api_response_to_excel.robot">
<test id="s1-t1" name="Parse API Response From JSON File And Write To Excel" line="49">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.386777" level="INFO">Creating new Excel file: output_addresses.xlsx</msg>
<arg>Creating new Excel file: ${EXCEL_FILE_PATH}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.386272" elapsed="0.003237"/>
</kw>
<kw name="Create Excel Workbook">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.580835" level="INFO">${workbook} = &lt;openpyxl.workbook.workbook.Workbook object at 0x10d8d0290&gt;</msg>
<var>${workbook}</var>
<arg>__import__('openpyxl').Workbook()</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.393582" elapsed="0.187541"/>
</kw>
<return>
<value>${workbook}</value>
<status status="PASS" start="2025-05-29T21:08:02.581327" elapsed="0.000156"/>
</return>
<return>
<value>${workbook}</value>
<status status="NOT RUN" start="2025-05-29T21:08:02.581621" elapsed="0.000106"/>
</return>
<msg time="2025-05-29T21:08:02.582087" level="INFO">${workbook} = &lt;openpyxl.workbook.workbook.Workbook object at 0x10d8d0290&gt;</msg>
<var>${workbook}</var>
<doc>Creates a new Excel workbook using openpyxl</doc>
<status status="PASS" start="2025-05-29T21:08:02.392633" elapsed="0.189639"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.582943" level="INFO">Reading API response from sample_api_response.json</msg>
<arg>Reading API response from sample_api_response.json</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.582660" elapsed="0.000448"/>
</kw>
<kw name="Get File" owner="OperatingSystem">
<msg time="2025-05-29T21:08:02.584168" level="INFO" html="true">Getting file '&lt;a href="file:///Users/<USER>/Documents/robot_api_extractdata/sample_api_response.json"&gt;/Users/<USER>/Documents/robot_api_extractdata/sample_api_response.json&lt;/a&gt;'.</msg>
<msg time="2025-05-29T21:08:02.585420" level="INFO">${json_content} = {
    "success": true,
    "status": "OK",
    "data": [
        {
            "index": 0,
            "specific_address": "ngo 268 75 pho doi can",
            "ghtk_addresses": [
                {
 ...</msg>
<var>${json_content}</var>
<arg>sample_api_response.json</arg>
<doc>Returns the contents of a specified file.</doc>
<status status="PASS" start="2025-05-29T21:08:02.583539" elapsed="0.002260"/>
</kw>
<kw name="Parse JSON String">
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.589835" level="INFO">${parsed_json} = {'success': True, 'status': 'OK', 'data': [{'index': 0, 'specific_address': 'ngo 268 75 pho doi can', 'ghtk_addresses': [{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}, {'name': 'Quận B...</msg>
<var>${parsed_json}</var>
<arg>__import__('json').loads('''${json_string}''')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.589070" elapsed="0.001238"/>
</kw>
<return>
<value>${parsed_json}</value>
<status status="PASS" start="2025-05-29T21:08:02.590505" elapsed="0.000152"/>
</return>
<return>
<value>${parsed_json}</value>
<status status="NOT RUN" start="2025-05-29T21:08:02.590855" elapsed="0.000178"/>
</return>
<msg time="2025-05-29T21:08:02.591458" level="INFO">${parsed_json} = {'success': True, 'status': 'OK', 'data': [{'index': 0, 'specific_address': 'ngo 268 75 pho doi can', 'ghtk_addresses': [{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}, {'name': 'Quận B...</msg>
<var>${parsed_json}</var>
<arg>${json_content}</arg>
<doc>Parses a JSON string using Python's json module</doc>
<status status="PASS" start="2025-05-29T21:08:02.588308" elapsed="0.003444"/>
</kw>
<kw name="Validate API Response Structure">
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.601094" level="INFO">${success} = True</msg>
<var>${success}</var>
<arg>${json_data}</arg>
<arg>success</arg>
<arg>default=${False}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.597297" elapsed="0.004153"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${success}</arg>
<arg>API response indicates failure</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2025-05-29T21:08:02.601826" elapsed="0.004648"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.608906" level="INFO">False</msg>
<arg>${json_data}</arg>
<arg>data</arg>
<arg>API response missing 'data' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.607273" elapsed="0.002496"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.612551" level="INFO">${data} = [{'index': 0, 'specific_address': 'ngo 268 75 pho doi can', 'ghtk_addresses': [{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}, {'name': 'Quận Ba Đình', 'type': 3, 'address_id': 2}, {'na...</msg>
<var>${data}</var>
<arg>${json_data}</arg>
<arg>data</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.610384" elapsed="0.002529"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.614456" level="INFO">${is_list} = True</msg>
<var>${is_list}</var>
<arg>isinstance($data, list)</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.613748" elapsed="0.000890"/>
</kw>
<kw name="Should Be True" owner="BuiltIn">
<arg>${is_list}</arg>
<arg>'data' field should be a list</arg>
<doc>Fails if the given condition is not true.</doc>
<status status="PASS" start="2025-05-29T21:08:02.614862" elapsed="0.000474"/>
</kw>
<arg>${parsed_json}</arg>
<doc>Validates that the API response has the expected structure</doc>
<status status="PASS" start="2025-05-29T21:08:02.595274" elapsed="0.020323"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.620168" level="INFO">${data_items} = [{'index': 0, 'specific_address': 'ngo 268 75 pho doi can', 'ghtk_addresses': [{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}, {'name': 'Quận Ba Đình', 'type': 3, 'address_id': 2}, {'na...</msg>
<var>${data_items}</var>
<arg>${parsed_json}</arg>
<arg>data</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.618898" elapsed="0.001837"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.622868" level="INFO">Length is 2.</msg>
<msg time="2025-05-29T21:08:02.623543" level="INFO">${data_count} = 2</msg>
<var>${data_count}</var>
<arg>${data_items}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-05-29T21:08:02.621871" elapsed="0.002030"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.625152" level="INFO">Found 2 data items in API response</msg>
<arg>Found ${data_count} data items in API response</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.624615" elapsed="0.001080"/>
</kw>
<for flavor="IN ENUMERATE">
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.631561" level="INFO">Processing data item 1 of 2</msg>
<arg>Processing data item ${index + 1} of ${data_count}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.630602" elapsed="0.001195"/>
</kw>
<kw name="Process Data Item">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.633812" level="INFO">False</msg>
<arg>${data_item}</arg>
<arg>ghtk_addresses</arg>
<arg>Data item ${item_index} missing 'ghtk_addresses' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.633465" elapsed="0.000501"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.634583" level="INFO">${ghtk_addresses} = [{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}, {'name': 'Quận Ba Đình', 'type': 3, 'address_id': 2}, {'name': 'Hà Nội', 'type': 0, 'address_id': 1}, {'name': 'Đội Cấn', 'type': 2, 'ad...</msg>
<var>${ghtk_addresses}</var>
<arg>${data_item}</arg>
<arg>ghtk_addresses</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.634153" elapsed="0.000588"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.635189" level="INFO">Length is 5.</msg>
<msg time="2025-05-29T21:08:02.635541" level="INFO">${address_count} = 5</msg>
<var>${address_count}</var>
<arg>${ghtk_addresses}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-05-29T21:08:02.634952" elapsed="0.000880"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.636667" level="INFO">Found 5 addresses in data item 0</msg>
<arg>Found ${address_count} addresses in data item ${item_index}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.636123" elapsed="0.000975"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.638118" level="INFO">${worksheet} = &lt;Worksheet "Sheet"&gt;</msg>
<var>${worksheet}</var>
<arg>$workbook.active</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.637630" elapsed="0.000644"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.639176" level="INFO">${type_10_row} = 1</msg>
<var>${type_10_row}</var>
<arg>${1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.638479" elapsed="0.000877"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.640095" level="INFO">${type_1_row} = 1</msg>
<var>${type_1_row}</var>
<arg>${1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.639566" elapsed="0.000668"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.641013" level="INFO">${type_3_row} = 1</msg>
<var>${type_3_row}</var>
<arg>${1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.640487" elapsed="0.000720"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.643430" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.643049" elapsed="0.000942"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.645131" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.644332" elapsed="0.001042"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.646334" level="INFO">${name} = Lẻ 1-135 Đội Cấn</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.645741" elapsed="0.000880"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.647355" level="INFO">${type} = 29</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.646915" elapsed="0.000602"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.648017" level="INFO">Processing address: Lẻ 1-135 Đội Cấn (type: 29)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.647740" elapsed="0.000462"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.648774" elapsed="0.000207"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.649443" elapsed="0.000157"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.649891" elapsed="0.000171"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.650389" elapsed="0.000210"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.648404" elapsed="0.002393"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.651660" elapsed="0.000253"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.652191" elapsed="0.000112"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.652480" elapsed="0.000102"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.652757" elapsed="0.000110"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.650934" elapsed="0.002033"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.653349" elapsed="0.000105"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.653712" elapsed="0.000110"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.653998" elapsed="0.000100"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.654262" elapsed="0.000105"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.653072" elapsed="0.001534"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.655296" level="WARN">Skipping address 'Lẻ 1-135 Đội Cấn' with unsupported type: 29</msg>
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.654983" elapsed="0.000846"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.654750" elapsed="0.001383"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.648371" elapsed="0.008043"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.656565" elapsed="0.000142"/>
</return>
<msg time="2025-05-29T21:08:02.657106" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.657333" level="INFO">${type_1_row} = 1</msg>
<msg time="2025-05-29T21:08:02.657564" level="INFO">${type_3_row} = 1</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.642337" elapsed="0.015372"/>
</kw>
<var name="${address}">{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}</var>
<status status="PASS" start="2025-05-29T21:08:02.641762" elapsed="0.016219"/>
</iter>
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.660393" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.659939" elapsed="0.000721"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.662745" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.660865" elapsed="0.002185"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.664204" level="INFO">${name} = Quận Ba Đình</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.663485" elapsed="0.000894"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.665006" level="INFO">${type} = 3</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.664618" elapsed="0.000532"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.665639" level="INFO">Processing address: Quận Ba Đình (type: 3)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.665361" elapsed="0.000432"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.667091" elapsed="0.000275"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.667841" elapsed="0.000199"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.668374" elapsed="0.000109"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.668805" elapsed="0.000112"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.665966" elapsed="0.003057"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.669415" elapsed="0.000105"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.669790" elapsed="0.000166"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.670160" elapsed="0.000104"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.670437" elapsed="0.000105"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.669126" elapsed="0.001521"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.671487" level="INFO">${cell} = C1</msg>
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.671179" elapsed="0.000454"/>
</kw>
<kw name="Write Excel Cell">
<try>
<branch type="TRY">
<kw name="Evaluate" owner="BuiltIn">
<arg>setattr($worksheet['${cell}'], 'value', '''${value}''')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.672826" elapsed="0.000571"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.674088" level="INFO">Successfully wrote 'Quận Ba Đình' to cell C1</msg>
<arg>Successfully wrote '${value}' to cell ${cell}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.673788" elapsed="0.000453"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.672425" elapsed="0.001933"/>
</branch>
<branch type="EXCEPT" assign="${error}">
<kw name="Log" owner="BuiltIn">
<arg>Failed to write to cell ${cell}: ${error}</arg>
<arg>ERROR</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.674794" elapsed="0.000176"/>
</kw>
<kw name="Fail" owner="BuiltIn">
<arg>Excel write operation failed: ${error}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.675297" elapsed="0.000170"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.674537" elapsed="0.001074"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.672370" elapsed="0.003478"/>
</try>
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="PASS" start="2025-05-29T21:08:02.671906" elapsed="0.004103"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.676684" level="INFO">Written 'Quận Ba Đình' to cell C1 (type 3)</msg>
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.676308" elapsed="0.000610"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.677775" level="INFO">${type_3_row} = 2</msg>
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.677331" elapsed="0.000915"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.670839" elapsed="0.007632"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.678933" elapsed="0.000144"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.678664" elapsed="0.000534"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.665939" elapsed="0.013524"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.679539" elapsed="0.000145"/>
</return>
<msg time="2025-05-29T21:08:02.679989" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.680138" level="INFO">${type_1_row} = 1</msg>
<msg time="2025-05-29T21:08:02.680279" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.659045" elapsed="0.021492"/>
</kw>
<var name="${address}">{'name': 'Quận Ba Đình', 'type': 3, 'address_id': 2}</var>
<status status="PASS" start="2025-05-29T21:08:02.658363" elapsed="0.022387"/>
</iter>
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.683057" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.682141" elapsed="0.001385"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.685301" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.684599" elapsed="0.000944"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.686316" level="INFO">${name} = Hà Nội</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.685789" elapsed="0.000703"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.687497" level="INFO">${type} = 0</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.686686" elapsed="0.001019"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.688622" level="INFO">Processing address: Hà Nội (type: 0)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.688186" elapsed="0.000712"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.689493" elapsed="0.000318"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.690267" elapsed="0.000298"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.690830" elapsed="0.000114"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.691131" elapsed="0.000113"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.689073" elapsed="0.002319"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.691958" elapsed="0.000154"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.692634" elapsed="0.000184"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.693101" elapsed="0.000155"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.693480" elapsed="0.000138"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.691520" elapsed="0.002229"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.694181" elapsed="0.000105"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.694861" elapsed="0.000281"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.695692" elapsed="0.000157"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.696379" elapsed="0.000299"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.693894" elapsed="0.002919"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.697529" level="WARN">Skipping address 'Hà Nội' with unsupported type: 0</msg>
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.697180" elapsed="0.001613"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.696955" elapsed="0.002154"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.689049" elapsed="0.010234"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.699343" elapsed="0.000174"/>
</return>
<msg time="2025-05-29T21:08:02.700290" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.700618" level="INFO">${type_1_row} = 1</msg>
<msg time="2025-05-29T21:08:02.701048" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.681546" elapsed="0.019766"/>
</kw>
<var name="${address}">{'name': 'Hà Nội', 'type': 0, 'address_id': 1}</var>
<status status="PASS" start="2025-05-29T21:08:02.680996" elapsed="0.020748"/>
</iter>
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.703841" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.703511" elapsed="0.000502"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.704474" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.704214" elapsed="0.000398"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.705162" level="INFO">${name} = Đội Cấn</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.704799" elapsed="0.000497"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.705915" level="INFO">${type} = 2</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.705542" elapsed="0.000668"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.706684" level="INFO">Processing address: Đội Cấn (type: 2)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.706425" elapsed="0.000421"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.708512" elapsed="0.000355"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.709687" elapsed="0.000354"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.710352" elapsed="0.000136"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.710715" elapsed="0.000115"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.707327" elapsed="0.003613"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.711389" elapsed="0.000228"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.712013" elapsed="0.000140"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.712782" elapsed="0.000188"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.713695" elapsed="0.000185"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.711053" elapsed="0.003031"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.714550" elapsed="0.000108"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.715008" elapsed="0.000122"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.715481" elapsed="0.000166"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.716033" elapsed="0.000187"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.714209" elapsed="0.002144"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.716985" level="WARN">Skipping address 'Đội Cấn' with unsupported type: 2</msg>
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.716670" elapsed="0.001509"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.716469" elapsed="0.003025"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.707251" elapsed="0.012688"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.720061" elapsed="0.000265"/>
</return>
<msg time="2025-05-29T21:08:02.720770" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.720939" level="INFO">${type_1_row} = 1</msg>
<msg time="2025-05-29T21:08:02.721073" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.702654" elapsed="0.018521"/>
</kw>
<var name="${address}">{'name': 'Đội Cấn', 'type': 2, 'address_id': 151}</var>
<status status="PASS" start="2025-05-29T21:08:02.702209" elapsed="0.019096"/>
</iter>
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.722803" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.722494" elapsed="0.000471"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.723577" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.723256" elapsed="0.000617"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.725225" level="INFO">${name} = Phường Đội Cấn</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.724248" elapsed="0.001304"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.726813" level="INFO">${type} = 1</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.726184" elapsed="0.000914"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.727816" level="INFO">Processing address: Phường Đội Cấn (type: 1)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.727373" elapsed="0.000620"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.728555" elapsed="0.000183"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.729108" elapsed="0.000141"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.729456" elapsed="0.000200"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.729852" elapsed="0.000208"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.728186" elapsed="0.002154"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.731106" level="INFO">${cell} = B1</msg>
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.730771" elapsed="0.000599"/>
</kw>
<kw name="Write Excel Cell">
<try>
<branch type="TRY">
<kw name="Evaluate" owner="BuiltIn">
<arg>setattr($worksheet['${cell}'], 'value', '''${value}''')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.733102" elapsed="0.000447"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.734006" level="INFO">Successfully wrote 'Phường Đội Cấn' to cell B1</msg>
<arg>Successfully wrote '${value}' to cell ${cell}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.733743" elapsed="0.000411"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.732902" elapsed="0.001373"/>
</branch>
<branch type="EXCEPT" assign="${error}">
<kw name="Log" owner="BuiltIn">
<arg>Failed to write to cell ${cell}: ${error}</arg>
<arg>ERROR</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.735121" elapsed="0.000133"/>
</kw>
<kw name="Fail" owner="BuiltIn">
<arg>Excel write operation failed: ${error}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.735439" elapsed="0.000099"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.734510" elapsed="0.001135"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.732850" elapsed="0.002916"/>
</try>
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="PASS" start="2025-05-29T21:08:02.732377" elapsed="0.003458"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.736276" level="INFO">Written 'Phường Đội Cấn' to cell B1 (type 1)</msg>
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.736024" elapsed="0.000409"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.736924" level="INFO">${type_1_row} = 2</msg>
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.736615" elapsed="0.000437"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.730464" elapsed="0.006710"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.737470" elapsed="0.000106"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.738239" elapsed="0.000130"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.738570" elapsed="0.000104"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.739047" elapsed="0.000141"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.737281" elapsed="0.002114"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.740056" elapsed="0.000669"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.739672" elapsed="0.001450"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.728155" elapsed="0.013793"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.742044" elapsed="0.000200"/>
</return>
<msg time="2025-05-29T21:08:02.742734" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.742981" level="INFO">${type_1_row} = 2</msg>
<msg time="2025-05-29T21:08:02.743157" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.721889" elapsed="0.021403"/>
</kw>
<var name="${address}">{'name': 'Phường Đội Cấn', 'type': 1, 'address_id': 33}</var>
<status status="PASS" start="2025-05-29T21:08:02.721518" elapsed="0.021942"/>
</iter>
<var>${address}</var>
<value>@{ghtk_addresses}</value>
<status status="PASS" start="2025-05-29T21:08:02.641380" elapsed="0.102252"/>
</for>
<arg>${data_item}</arg>
<arg>${index}</arg>
<arg>${workbook}</arg>
<doc>Processes a single data item and extracts ghtk_addresses</doc>
<status status="PASS" start="2025-05-29T21:08:02.632321" elapsed="0.111541"/>
</kw>
<var name="${index}">0</var>
<var name="${data_item}">{'index': 0, 'specific_address': 'ngo 268 75 pho doi can', 'ghtk_addresses': [{'name': 'Lẻ 1-135 Đội Cấn', 'type': 29, 'address_id': 86996}, {'name': 'Quận Ba Đình', 'type': 3, 'address_id': 2}, {'nam...</var>
<status status="PASS" start="2025-05-29T21:08:02.628366" elapsed="0.115642"/>
</iter>
<iter>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.746148" level="INFO">Processing data item 2 of 2</msg>
<arg>Processing data item ${index + 1} of ${data_count}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.744733" elapsed="0.001869"/>
</kw>
<kw name="Process Data Item">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.747740" level="INFO">False</msg>
<arg>${data_item}</arg>
<arg>ghtk_addresses</arg>
<arg>Data item ${item_index} missing 'ghtk_addresses' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.747419" elapsed="0.000616"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.749599" level="INFO">${ghtk_addresses} = [{'name': 'Test District', 'type': 3, 'address_id': 999}, {'name': 'Test Ward', 'type': 1, 'address_id': 888}, {'name': 'Special Location', 'type': 10, 'address_id': 777}]</msg>
<var>${ghtk_addresses}</var>
<arg>${data_item}</arg>
<arg>ghtk_addresses</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.749142" elapsed="0.000790"/>
</kw>
<kw name="Get Length" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.750363" level="INFO">Length is 3.</msg>
<msg time="2025-05-29T21:08:02.750535" level="INFO">${address_count} = 3</msg>
<var>${address_count}</var>
<arg>${ghtk_addresses}</arg>
<doc>Returns and logs the length of the given item as an integer.</doc>
<status status="PASS" start="2025-05-29T21:08:02.750147" elapsed="0.000498"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.751066" level="INFO">Found 3 addresses in data item 1</msg>
<arg>Found ${address_count} addresses in data item ${item_index}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.750824" elapsed="0.000382"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.751714" level="INFO">${worksheet} = &lt;Worksheet "Sheet"&gt;</msg>
<var>${worksheet}</var>
<arg>$workbook.active</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.751387" elapsed="0.000442"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.752477" level="INFO">${type_10_row} = 1</msg>
<var>${type_10_row}</var>
<arg>${1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.752009" elapsed="0.000583"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.753983" level="INFO">${type_1_row} = 1</msg>
<var>${type_1_row}</var>
<arg>${1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.752772" elapsed="0.001395"/>
</kw>
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.755152" level="INFO">${type_3_row} = 1</msg>
<var>${type_3_row}</var>
<arg>${1}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.754468" elapsed="0.000886"/>
</kw>
<for flavor="IN">
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.758239" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.757760" elapsed="0.000720"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.759154" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.758767" elapsed="0.000583"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.760373" level="INFO">${name} = Test District</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.759593" elapsed="0.000925"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.761106" level="INFO">${type} = 3</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.760724" elapsed="0.000530"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.761748" level="INFO">Processing address: Test District (type: 3)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.761482" elapsed="0.000440"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.762655" elapsed="0.000162"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.763196" elapsed="0.000191"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.763601" elapsed="0.000106"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.763885" elapsed="0.000123"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.762218" elapsed="0.001904"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.764684" elapsed="0.000122"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.765568" elapsed="0.000145"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.765935" elapsed="0.000103"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.766213" elapsed="0.000105"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.764337" elapsed="0.002085"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.767112" level="INFO">${cell} = C1</msg>
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.766805" elapsed="0.000432"/>
</kw>
<kw name="Write Excel Cell">
<try>
<branch type="TRY">
<kw name="Evaluate" owner="BuiltIn">
<arg>setattr($worksheet['${cell}'], 'value', '''${value}''')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.768118" elapsed="0.000403"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.768948" level="INFO">Successfully wrote 'Test District' to cell C1</msg>
<arg>Successfully wrote '${value}' to cell ${cell}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.768707" elapsed="0.000522"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.767939" elapsed="0.001411"/>
</branch>
<branch type="EXCEPT" assign="${error}">
<kw name="Log" owner="BuiltIn">
<arg>Failed to write to cell ${cell}: ${error}</arg>
<arg>ERROR</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.769657" elapsed="0.000107"/>
</kw>
<kw name="Fail" owner="BuiltIn">
<arg>Excel write operation failed: ${error}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.769939" elapsed="0.000182"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.769471" elapsed="0.000768"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.767903" elapsed="0.002457"/>
</try>
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="PASS" start="2025-05-29T21:08:02.767507" elapsed="0.002927"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.770872" level="INFO">Written 'Test District' to cell C1 (type 3)</msg>
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.770625" elapsed="0.000396"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.771526" level="INFO">${type_3_row} = 2</msg>
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.771208" elapsed="0.000436"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.766527" elapsed="0.005233"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.772079" elapsed="0.000188"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.771872" elapsed="0.000510"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.762190" elapsed="0.010330"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.772597" elapsed="0.000146"/>
</return>
<msg time="2025-05-29T21:08:02.773198" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.773371" level="INFO">${type_1_row} = 1</msg>
<msg time="2025-05-29T21:08:02.773532" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.757018" elapsed="0.016631"/>
</kw>
<var name="${address}">{'name': 'Test District', 'type': 3, 'address_id': 999}</var>
<status status="PASS" start="2025-05-29T21:08:02.755838" elapsed="0.017955"/>
</iter>
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.775896" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.775392" elapsed="0.000765"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.777008" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.776559" elapsed="0.000601"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.777772" level="INFO">${name} = Test Ward</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.777359" elapsed="0.000570"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.778579" level="INFO">${type} = 1</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.778186" elapsed="0.000769"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.779541" level="INFO">Processing address: Test Ward (type: 1)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.779256" elapsed="0.000446"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.781067" elapsed="0.000150"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.781531" elapsed="0.000124"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.781855" elapsed="0.000127"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.782163" elapsed="0.000113"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.780181" elapsed="0.002200"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.783059" level="INFO">${cell} = B1</msg>
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.782768" elapsed="0.000421"/>
</kw>
<kw name="Write Excel Cell">
<try>
<branch type="TRY">
<kw name="Evaluate" owner="BuiltIn">
<arg>setattr($worksheet['${cell}'], 'value', '''${value}''')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.784051" elapsed="0.000392"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.784877" level="INFO">Successfully wrote 'Test Ward' to cell B1</msg>
<arg>Successfully wrote '${value}' to cell ${cell}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.784627" elapsed="0.000376"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.783881" elapsed="0.001227"/>
</branch>
<branch type="EXCEPT" assign="${error}">
<kw name="Log" owner="BuiltIn">
<arg>Failed to write to cell ${cell}: ${error}</arg>
<arg>ERROR</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.785399" elapsed="0.000104"/>
</kw>
<kw name="Fail" owner="BuiltIn">
<arg>Excel write operation failed: ${error}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.785676" elapsed="0.000097"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.785222" elapsed="0.000660"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.783847" elapsed="0.002148"/>
</try>
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="PASS" start="2025-05-29T21:08:02.783460" elapsed="0.002795"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.786728" level="INFO">Written 'Test Ward' to cell B1 (type 1)</msg>
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.786473" elapsed="0.000409"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.787510" level="INFO">${type_1_row} = 2</msg>
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.787134" elapsed="0.000497"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.782487" elapsed="0.005261"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.788038" elapsed="0.000106"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.788510" elapsed="0.000138"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.788972" elapsed="0.000124"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.789303" elapsed="0.000115"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.787852" elapsed="0.001683"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.789844" elapsed="0.000112"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.789656" elapsed="0.000413"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.780142" elapsed="0.010032"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.790230" elapsed="0.000180"/>
</return>
<msg time="2025-05-29T21:08:02.790727" level="INFO">${type_10_row} = 1</msg>
<msg time="2025-05-29T21:08:02.790884" level="INFO">${type_1_row} = 2</msg>
<msg time="2025-05-29T21:08:02.791022" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.774429" elapsed="0.016745"/>
</kw>
<var name="${address}">{'name': 'Test Ward', 'type': 1, 'address_id': 888}</var>
<status status="PASS" start="2025-05-29T21:08:02.774007" elapsed="0.017330"/>
</iter>
<iter>
<kw name="Process Address">
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.794787" level="INFO">False</msg>
<arg>${address}</arg>
<arg>name</arg>
<arg>Address missing 'name' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.792971" elapsed="0.001997"/>
</kw>
<kw name="Dictionary Should Contain Key" owner="Collections">
<msg time="2025-05-29T21:08:02.795609" level="INFO">False</msg>
<arg>${address}</arg>
<arg>type</arg>
<arg>Address missing 'type' field</arg>
<doc>Fails if ``key`` is not found from ``dictionary``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.795187" elapsed="0.000578"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.797287" level="INFO">${name} = Special Location</msg>
<var>${name}</var>
<arg>${address}</arg>
<arg>name</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.796044" elapsed="0.001431"/>
</kw>
<kw name="Get From Dictionary" owner="Collections">
<msg time="2025-05-29T21:08:02.798151" level="INFO">${type} = 10</msg>
<var>${type}</var>
<arg>${address}</arg>
<arg>type</arg>
<arg>default=${None}</arg>
<doc>Returns a value from the given ``dictionary`` based on the given ``key``.</doc>
<status status="PASS" start="2025-05-29T21:08:02.797721" elapsed="0.000639"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.798836" level="INFO">Processing address: Special Location (type: 10)</msg>
<arg>Processing address: ${name} (type: ${type})</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.798575" elapsed="0.000411"/>
</kw>
<if>
<branch type="IF" condition="${type} == 10">
<kw name="Set Variable" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.799701" level="INFO">${cell} = A1</msg>
<var>${cell}</var>
<arg>${TYPE_10_COLUMN}${type_10_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="PASS" start="2025-05-29T21:08:02.799422" elapsed="0.000402"/>
</kw>
<kw name="Write Excel Cell">
<try>
<branch type="TRY">
<kw name="Evaluate" owner="BuiltIn">
<arg>setattr($worksheet['${cell}'], 'value', '''${value}''')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.800690" elapsed="0.000407"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.801522" level="INFO">Successfully wrote 'Special Location' to cell A1</msg>
<arg>Successfully wrote '${value}' to cell ${cell}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.801284" elapsed="0.000363"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.800519" elapsed="0.001233"/>
</branch>
<branch type="EXCEPT" assign="${error}">
<kw name="Log" owner="BuiltIn">
<arg>Failed to write to cell ${cell}: ${error}</arg>
<arg>ERROR</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.802049" elapsed="0.000106"/>
</kw>
<kw name="Fail" owner="BuiltIn">
<arg>Excel write operation failed: ${error}</arg>
<doc>Fails the test with the given message and optionally alters its tags.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.802327" elapsed="0.000097"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.801867" elapsed="0.000660"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.800484" elapsed="0.002156"/>
</try>
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="PASS" start="2025-05-29T21:08:02.800099" elapsed="0.002604"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.803489" level="INFO">Written 'Special Location' to cell A1 (type 10)</msg>
<arg>Written '${name}' to cell ${cell} (type 10)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.803139" elapsed="0.000572"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.805225" level="INFO">${type_10_row} = 2</msg>
<var>${type_10_row}</var>
<arg>${type_10_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.804033" elapsed="0.001413"/>
</kw>
<status status="PASS" start="2025-05-29T21:08:02.799144" elapsed="0.006483"/>
</branch>
<branch type="ELSE IF" condition="${type} == 1">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_1_COLUMN}${type_1_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.806197" elapsed="0.000124"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.806957" elapsed="0.000247"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 1)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.807418" elapsed="0.000111"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_1_row}</var>
<arg>${type_1_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.807716" elapsed="0.000131"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.805946" elapsed="0.002019"/>
</branch>
<branch type="ELSE IF" condition="${type} == 3">
<kw name="Set Variable" owner="BuiltIn">
<var>${cell}</var>
<arg>${TYPE_3_COLUMN}${type_3_row}</arg>
<doc>Returns the given values which can then be assigned to a variables.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.808314" elapsed="0.000120"/>
</kw>
<kw name="Write Excel Cell">
<arg>${worksheet}</arg>
<arg>${cell}</arg>
<arg>${name}</arg>
<doc>Writes a value to a specific Excel cell using openpyxl</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.808788" elapsed="0.000141"/>
</kw>
<kw name="Log" owner="BuiltIn">
<arg>Written '${name}' to cell ${cell} (type 3)</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.809176" elapsed="0.000132"/>
</kw>
<kw name="Evaluate" owner="BuiltIn">
<var>${type_3_row}</var>
<arg>${type_3_row} + 1</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.809527" elapsed="0.000132"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.808096" elapsed="0.001693"/>
</branch>
<branch type="ELSE">
<kw name="Log" owner="BuiltIn">
<arg>Skipping address '${name}' with unsupported type: ${type}</arg>
<arg>WARN</arg>
<doc>Logs the given message with the given level.</doc>
<status status="NOT RUN" start="2025-05-29T21:08:02.810121" elapsed="0.000103"/>
</kw>
<status status="NOT RUN" start="2025-05-29T21:08:02.809926" elapsed="0.000405"/>
</branch>
<status status="PASS" start="2025-05-29T21:08:02.799119" elapsed="0.011309"/>
</if>
<return>
<value>${type_10_row}</value>
<value>${type_1_row}</value>
<value>${type_3_row}</value>
<status status="PASS" start="2025-05-29T21:08:02.810479" elapsed="0.000108"/>
</return>
<msg time="2025-05-29T21:08:02.810889" level="INFO">${type_10_row} = 2</msg>
<msg time="2025-05-29T21:08:02.811056" level="INFO">${type_1_row} = 2</msg>
<msg time="2025-05-29T21:08:02.811206" level="INFO">${type_3_row} = 2</msg>
<var>${type_10_row}</var>
<var>${type_1_row}</var>
<var>${type_3_row}</var>
<arg>${address}</arg>
<arg>${type_10_row}</arg>
<arg>${type_1_row}</arg>
<arg>${type_3_row}</arg>
<arg>${worksheet}</arg>
<doc>Processes a single address and writes to appropriate Excel cell based on type</doc>
<status status="PASS" start="2025-05-29T21:08:02.792239" elapsed="0.019076"/>
</kw>
<var name="${address}">{'name': 'Special Location', 'type': 10, 'address_id': 777}</var>
<status status="PASS" start="2025-05-29T21:08:02.791582" elapsed="0.019867"/>
</iter>
<var>${address}</var>
<value>@{ghtk_addresses}</value>
<status status="PASS" start="2025-05-29T21:08:02.755583" elapsed="0.055993"/>
</for>
<arg>${data_item}</arg>
<arg>${index}</arg>
<arg>${workbook}</arg>
<doc>Processes a single data item and extracts ghtk_addresses</doc>
<status status="PASS" start="2025-05-29T21:08:02.746927" elapsed="0.064838"/>
</kw>
<var name="${index}">1</var>
<var name="${data_item}">{'index': 1, 'specific_address': '123 test street', 'ghtk_addresses': [{'name': 'Test District', 'type': 3, 'address_id': 999}, {'name': 'Test Ward', 'type': 1, 'address_id': 888}, {'name': 'Special L...</var>
<status status="PASS" start="2025-05-29T21:08:02.744269" elapsed="0.067620"/>
</iter>
<var>${index}</var>
<var>${data_item}</var>
<value>@{data_items}</value>
<status status="PASS" start="2025-05-29T21:08:02.627092" elapsed="0.185184"/>
</for>
<kw name="Save Excel Workbook">
<kw name="Evaluate" owner="BuiltIn">
<arg>$workbook.save('${EXCEL_FILE_PATH}')</arg>
<doc>Evaluates the given expression in Python and returns the result.</doc>
<status status="PASS" start="2025-05-29T21:08:02.814233" elapsed="0.105027"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.920730" level="INFO">Excel file saved to: output_addresses.xlsx</msg>
<arg>Excel file saved to: ${EXCEL_FILE_PATH}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.919916" elapsed="0.001092"/>
</kw>
<arg>${workbook}</arg>
<doc>Saves the Excel workbook to a file</doc>
<status status="PASS" start="2025-05-29T21:08:02.813369" elapsed="0.107874"/>
</kw>
<kw name="Log" owner="BuiltIn">
<msg time="2025-05-29T21:08:02.923600" level="INFO">Successfully processed API response from file and saved to output_addresses.xlsx</msg>
<arg>Successfully processed API response from file and saved to ${EXCEL_FILE_PATH}</arg>
<doc>Logs the given message with the given level.</doc>
<status status="PASS" start="2025-05-29T21:08:02.922664" elapsed="0.001276"/>
</kw>
<doc>Test case to read API response from JSON file and write to Excel</doc>
<tag>api</tag>
<tag>excel</tag>
<tag>file</tag>
<tag>json</tag>
<status status="PASS" start="2025-05-29T21:08:02.384889" elapsed="0.542254"/>
</test>
<doc>Test case to parse API response JSON and write ghtk_addresses data to Excel based on type field</doc>
<status status="PASS" start="2025-05-29T21:08:02.320186" elapsed="0.610036"/>
</suite>
<statistics>
<total>
<stat pass="1" fail="0" skip="0">All Tests</stat>
</total>
<tag>
<stat pass="1" fail="0" skip="0">api</stat>
<stat pass="1" fail="0" skip="0">excel</stat>
<stat pass="1" fail="0" skip="0">file</stat>
<stat pass="1" fail="0" skip="0">json</stat>
</tag>
<suite>
<stat pass="1" fail="0" skip="0" id="s1" name="Test Api Response To Excel">Test Api Response To Excel</stat>
</suite>
</statistics>
<errors>
<msg time="2025-05-29T21:08:02.304071" level="WARN">Error in file '/Users/<USER>/Documents/robot_api_extractdata/test_api_response_to_excel.robot' on line 84: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-05-29T21:08:02.304490" level="WARN">Error in file '/Users/<USER>/Documents/robot_api_extractdata/test_api_response_to_excel.robot' on line 92: The '[Return]' setting is deprecated. Use the 'RETURN' statement instead.</msg>
<msg time="2025-05-29T21:08:02.655296" level="WARN">Skipping address 'Lẻ 1-135 Đội Cấn' with unsupported type: 29</msg>
<msg time="2025-05-29T21:08:02.697529" level="WARN">Skipping address 'Hà Nội' with unsupported type: 0</msg>
<msg time="2025-05-29T21:08:02.716985" level="WARN">Skipping address 'Đội Cấn' with unsupported type: 2</msg>
</errors>
</robot>
