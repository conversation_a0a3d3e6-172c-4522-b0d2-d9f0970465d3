# Robot Framework API Response to Excel Test

This project contains Robot Framework test cases that parse API response JSON data and write specific address information to Excel files based on address type.

## Features

- Parses JSON API responses containing address data
- Extracts `ghtk_addresses` arrays from data items
- Maps address types to specific Excel columns:
  - Type 10: Column A
  - Type 1: Column B  
  - Type 3: Column C
- Handles multiple addresses of the same type (writes to adjacent rows)
- Includes comprehensive error handling
- Supports both inline JSON data and JSON files

## Requirements

Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Project Structure

```
├── test_api_response_to_excel.robot  # Main Robot Framework test file
├── sample_api_response.json          # Sample JSON data for testing
├── requirements.txt                  # Python dependencies
└── README.md                        # This file
```

## Test Cases

### 1. Parse API Response And Write To Excel
- Uses inline JSON data defined in the test file
- Creates a new Excel workbook
- Parses the JSON response
- Writes address names to appropriate columns based on type

### 2. Parse API Response From JSON File And Write To Excel
- Reads JSON data from `sample_api_response.json`
- Same processing logic as the first test case
- Demonstrates file-based input

## Running the Tests

### Run all tests:
```bash
robot test_api_response_to_excel.robot
```

### Run specific test:
```bash
robot -t "Parse API Response And Write To Excel" test_api_response_to_excel.robot
```

### Run with tags:
```bash
robot -i api test_api_response_to_excel.robot
```

## Output

- **Excel File**: `output_addresses.xlsx` - Contains the processed address data
- **Robot Framework Reports**: 
  - `report.html` - Test execution report
  - `log.html` - Detailed test log
  - `output.xml` - Test results in XML format

## API Response Format

The test expects JSON responses with this structure:

```json
{
    "success": true,
    "status": "OK",
    "data": [
        {
            "index": 0,
            "specific_address": "address string",
            "ghtk_addresses": [
                {
                    "name": "Address Name",
                    "type": 1,
                    "address_id": 123
                }
            ]
        }
    ]
}
```

## Address Type Mapping

| Type | Excel Column | Description |
|------|-------------|-------------|
| 10   | A           | Special locations |
| 1    | B           | Ward level |
| 3    | C           | District level |

## Error Handling

The test includes error handling for:
- Invalid JSON format
- Missing required fields (`success`, `data`, `ghtk_addresses`, `name`, `type`)
- Excel write operations
- Unexpected data structures

## Customization

To modify the column mapping, update the variables in the test file:

```robot
${TYPE_10_COLUMN}    A    # Type 10 addresses go to column A
${TYPE_1_COLUMN}     B    # Type 1 addresses go to column B  
${TYPE_3_COLUMN}     C    # Type 3 addresses go to column C
```

## Libraries Used

- **Collections**: For handling lists and dictionaries
- **OperatingSystem**: For file operations
- **String**: For string manipulations
- **Python's json module**: For JSON parsing (via Evaluate keyword)
- **Python's openpyxl**: For Excel operations (via Evaluate keyword)

## Notes

- The test uses Python's built-in libraries via Robot Framework's `Evaluate` keyword
- Multiple addresses of the same type are written to adjacent rows in the same column
- Addresses with unsupported types are logged as warnings and skipped
- The Excel file is automatically saved after processing all data items
